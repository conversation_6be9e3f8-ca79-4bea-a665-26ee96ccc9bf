<?php
/**
 * Vista para el reporte de balance general
 *
 * Variables disponibles:
 * @var array|null $reporte_data Datos del reporte generado
 * @var string $mes_seleccionado Mes seleccionado
 * @var string $anio_seleccionado Año seleccionado
 * @var int|null $id_centro_costo ID del centro de costo de la sesión
 * @var string $centro_costo_nombre Nombre del centro de costo de la sesión
 * @var string|null $success_text Mensaje de éxito a mostrar
 * @var string|null $success_display Estado de visualización de éxito ('show' o null)
 * @var string|null $error_text Mensaje de error a mostrar
 * @var string|null $error_display Estado de visualización de error ('show' o null)
 */
?>

<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Reporte de Balance General</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Include Bootstrap Datepicker CSS -->
	<link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

        <!-- BEGIN #content -->
        <div id="content" class="app-content">
            <!-- BEGIN container -->
            <div class="container">
                <!-- BEGIN row -->
                <div class="row justify-content-center">
                    <!-- BEGIN col-12 -->
                    <div class="col-12">

                        <?php #region PAGE HEADER ?>
                        <div class="d-flex align-items-center mb-3">
                            <div>
                                <h4 class="mb-0">Reporte de Balance General</h4>
                                <p class="mb-0 text-muted">Balance diario de ingresos y egresos por mes</p>
                                <?php if ($centro_costo_nombre): ?>
                                    <p class="mb-0 text-info"><i class="fa fa-building me-1"></i>Centro de Costo: <?php echo htmlspecialchars($centro_costo_nombre); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <hr>
                        <?php #endregion PAGE HEADER ?>

                        <?php #region FILTERS PANEL ?>
                        <!-- Filters Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-heading">
                                <h4 class="panel-title">Filtros de Consulta</h4>
                            </div>
                            <div class="panel-body">
                                <form method="POST" id="reporte-form">
                                    <input type="hidden" name="action" value="generar_reporte">

                                    <div class="row g-3">
                                        <!-- Mes -->
                                        <div class="col-md-3">
                                            <label for="mes_seleccionado" class="form-label">Mes <span class="text-danger">*</span></label>
                                            <select class="form-select" id="mes_seleccionado" name="mes_seleccionado" required>
                                                <option value="">Seleccione un mes...</option>
                                                <option value="1" <?php echo ($mes_seleccionado == '1') ? 'selected' : ''; ?>>1 - Enero</option>
                                                <option value="2" <?php echo ($mes_seleccionado == '2') ? 'selected' : ''; ?>>2 - Febrero</option>
                                                <option value="3" <?php echo ($mes_seleccionado == '3') ? 'selected' : ''; ?>>3 - Marzo</option>
                                                <option value="4" <?php echo ($mes_seleccionado == '4') ? 'selected' : ''; ?>>4 - Abril</option>
                                                <option value="5" <?php echo ($mes_seleccionado == '5') ? 'selected' : ''; ?>>5 - Mayo</option>
                                                <option value="6" <?php echo ($mes_seleccionado == '6') ? 'selected' : ''; ?>>6 - Junio</option>
                                                <option value="7" <?php echo ($mes_seleccionado == '7') ? 'selected' : ''; ?>>7 - Julio</option>
                                                <option value="8" <?php echo ($mes_seleccionado == '8') ? 'selected' : ''; ?>>8 - Agosto</option>
                                                <option value="9" <?php echo ($mes_seleccionado == '9') ? 'selected' : ''; ?>>9 - Septiembre</option>
                                                <option value="10" <?php echo ($mes_seleccionado == '10') ? 'selected' : ''; ?>>10 - Octubre</option>
                                                <option value="11" <?php echo ($mes_seleccionado == '11') ? 'selected' : ''; ?>>11 - Noviembre</option>
                                                <option value="12" <?php echo ($mes_seleccionado == '12') ? 'selected' : ''; ?>>12 - Diciembre</option>
                                            </select>
                                        </div>

                                        <!-- Año -->
                                        <div class="col-md-3">
                                            <label for="anio_seleccionado" class="form-label">Año <span class="text-danger">*</span></label>
                                            <select class="form-select" id="anio_seleccionado" name="anio_seleccionado" required>
                                                <option value="">Seleccione un año...</option>
                                                <?php 
                                                $anio_actual = date('Y');
                                                for ($anio = 2020; $anio <= 2030; $anio++): 
                                                ?>
                                                    <option value="<?php echo $anio; ?>" <?php echo ($anio_seleccionado == $anio) ? 'selected' : ''; ?>>
                                                        <?php echo $anio; ?>
                                                    </option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>

                                        <!-- Botón Generar -->
                                        <div class="col-md-3 d-flex align-items-end">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fa fa-chart-line fa-fw me-1"></i> Generar Reporte
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Nota explicativa -->
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <div class="alert alert-info mb-0">
                                                <i class="fa fa-info-circle me-1"></i>
                                                <strong>Nota:</strong> Ingresos incluyen citas y ventas. Egresos incluyen órdenes de compra y gastos operativos.
                                                Solo se incluyen registros finalizados (con cierre asignado).
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <?php #endregion FILTERS PANEL ?>

                        <?php #region RESULTS ?>
                        <?php if ($reporte_data !== null && !empty($reporte_data['dias_balance'])): ?>
                        <!-- Results Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-heading">
                                <div class="d-flex align-items-center w-100">
                                    <div>
                                        <h4 class="panel-title mb-0">
                                            Balance General - <?php echo htmlspecialchars($reporte_data['mes_nombre'] ?? ''); ?> <?php echo htmlspecialchars($reporte_data['anio'] ?? ''); ?> <br>
                                            <span class="text-muted fs-6">
                                                <?php echo htmlspecialchars($centro_costo_nombre); ?>
                                            </span>
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm mb-0" id="balance-table">
                                        <thead class="table-dark">
                                            <tr>
                                                <th style="width: 80px;">Día</th>
                                                <th class="text-end">Ingresos</th>
                                                <th class="text-end">Egresos</th>
                                                <th class="text-end">Balance</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($reporte_data['dias_balance'] as $dia_data): ?>
                                                <tr>
                                                    <td class="align-middle"><strong><?php echo $dia_data['dia']; ?></strong></td>
                                                    <td class="text-end align-middle"><?php echo htmlspecialchars($dia_data['ingresos_formateado']); ?></td>
                                                    <td class="text-end align-middle"><?php echo htmlspecialchars($dia_data['egresos_formateado']); ?></td>
                                                    <td class="text-end align-middle <?php echo $dia_data['balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <strong><?php echo htmlspecialchars($dia_data['balance_formateado']); ?></strong>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot class="table-dark">
                                            <tr>
                                                <td><strong>TOTALES</strong></td>
                                                <td class="text-end"><strong><?php echo htmlspecialchars($reporte_data['total_ingresos_formateado']); ?></strong></td>
                                                <td class="text-end"><strong><?php echo htmlspecialchars($reporte_data['total_egresos_formateado']); ?></strong></td>
                                                <td class="text-end <?php echo $reporte_data['balance_total'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                    <strong><?php echo htmlspecialchars($reporte_data['balance_total_formateado']); ?></strong>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <?php elseif ($reporte_data !== null): ?>
                        <!-- Empty State Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-body text-center py-5">
                                <i class="fa fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No hay datos para mostrar</h5>
                                <p class="text-muted">No se encontraron registros finalizados para el mes y año seleccionados.</p>
                            </div>
                        </div>

                        <?php else: ?>
                        <!-- Initial State Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-body text-center py-5">
                                <i class="fa fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Seleccione un mes y año para generar el reporte</h5>
                                <p class="text-muted">Complete los filtros de consulta y haga clic en "Generar Reporte" para ver el balance diario.</p>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php #endregion RESULTS ?>

                    </div>
                    <!-- END col-12 -->
                </div>
                <!-- END row -->
            </div>
            <!-- END container -->
        </div>
        <!-- END #content -->

        <!-- BEGIN scroll-top-btn -->
        <a href="javascript:;" class="btn btn-icon btn-circle btn-theme btn-scroll-to-top" data-toggle="scroll-to-top">
            <i class="fa fa-angle-up"></i>
        </a>
        <!-- END scroll-top-btn -->
    </div>
    <!-- END #app -->

    <?php #region JS ?>
    <?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    document.getElementById('reporte-form').addEventListener('submit', function(e) {
        const mes = document.getElementById('mes_seleccionado').value;
        const anio = document.getElementById('anio_seleccionado').value;

        if (!mes || !anio) {
            e.preventDefault();
            showSweetAlertError('Error de Validación', 'Debe seleccionar un mes y año.');
            return false;
        }
    });

    // Handle Success/Error Messages from PHP
    <?php if (isset($success_display) && $success_display === 'show'): ?>
    showSweetAlertSuccess('¡Éxito!', '<?php echo addslashes($success_text ?? "Operación completada con éxito."); ?>');
    <?php endif; ?>

    <?php if (isset($error_display) && $error_display === 'show'): ?>
    showSweetAlertError('Error', '<?php echo addslashes($error_text ?? "Ha ocurrido un error."); ?>');
    <?php endif; ?>
});
</script>

</body>

</html>
